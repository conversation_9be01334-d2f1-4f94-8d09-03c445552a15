import 'package:mcdc/core/api/api_client.dart';
import 'package:mcdc/core/api/api_endpoints.dart';
import 'package:mcdc/core/api/api_result_handler.dart';
import 'package:mcdc/core/api/model/result.dart';
import 'package:mcdc/core/api/model/api_response_parser.dart';
import 'package:mcdc/core/error/exceptions.dart';
import 'package:mcdc/features/user/data/models/login_data_model.dart';
import 'package:mcdc/features/user/data/models/check_id_card_result_model.dart';
import 'package:mcdc/features/user/data/models/member_register_request_model.dart';
import 'package:mcdc/features/user/data/models/member_register_response_model.dart';
import 'package:mcdc/features/user/data/models/register_validation_error_model.dart';
import 'package:mcdc/features/user/data/models/forgot_password_response_model.dart';
import 'package:mcdc/features/user/data/models/forgot_password_verify_otp_response_model.dart';
import 'package:mcdc/features/user/data/models/create_password_request_model.dart';
import 'package:mcdc/features/user/data/models/create_password_response_model.dart';

abstract class UserApiDataSource {
  Future<LoginDataModel> memberLogin({
    required String username,
    required String password,
  });

  Future<LoginDataModel> consultantLogin({
    required String username,
    required String password,
  });

  Future<CheckIdCardResultModel> memberCheckIdCard({required String idCard});

  Future<RegisterValidationErrorModel> validateMemberRegister({
    required String username,
    required String email,
    required String phone,
  });

  Future<MemberRegisterResponseModel> memberRegister({
    required MemberRegisterRequestModel request,
  });

  Future<ForgotPasswordResponseModel> forgotPasswordRequest({
    required String email,
  });

  Future<ForgotPasswordVerifyOtpResponseModel> forgotPasswordVerifyOtp({
    required String email,
    required String otp,
    required String refCode,
    required String otpToken,
  });

  Future<CreatePasswordResponseModel> createPassword({
    required CreatePasswordRequestModel request,
  });

  Future<Result<void>> logout({required String token});
}

class UserApiDataSourceImpl implements UserApiDataSource {
  final ApiClient _apiClient;

  const UserApiDataSourceImpl(this._apiClient);

  @override
  Future<LoginDataModel> memberLogin({
    required String username,
    required String password,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.memberLogin,
      data: {'username': username, 'password': password},
    );

    if (result.isSuccess) {
      // Parser now returns data directly and throws ParseException on errors
      return ApiResponseParser.parseFlexibleResponse<LoginDataModel>(
        result.data,
        LoginDataModel.fromJson,
      );
    } else {
      throw ApiResultHandler.resultToException(result);
    }
  }

  @override
  Future<LoginDataModel> consultantLogin({
    required String username,
    required String password,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.consultantLogin,
      data: {'username': username, 'password': password},
    );

    if (result.isSuccess) {
      // Parser now returns data directly and throws ParseException on errors
      return ApiResponseParser.parseFlexibleResponse<LoginDataModel>(
        result.data,
        LoginDataModel.fromJson,
      );
    } else {
      // Convert API client error to appropriate exception
      final errorMessage = result.errorMessage ?? 'Login failed';
      final errorCode = result.errorCode;

      if (errorCode == '401' || errorCode == '403') {
        throw AuthException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      } else if (errorCode == '422' || errorCode == '400') {
        throw ValidationException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      } else {
        throw ServerException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      }
    }
  }

  @override
  Future<CheckIdCardResultModel> memberCheckIdCard({
    required String idCard,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.memberCheckIdCard,
      data: {'pid': idCard},
    );

    if (result.isSuccess) {
      // Parser now returns data directly and throws ParseException on errors
      return ApiResponseParser.parseFlexibleResponse<CheckIdCardResultModel>(
        result.data,
        CheckIdCardResultModel.fromJson,
      );
    } else {
      // Convert API client error to appropriate exception
      final errorMessage = result.errorMessage ?? 'ID card check failed';
      final errorCode = result.errorCode;

      if (errorCode == '422' || errorCode == '400') {
        throw ValidationException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      } else {
        throw ServerException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      }
    }
  }

  @override
  Future<RegisterValidationErrorModel> validateMemberRegister({
    required String username,
    required String email,
    required String phone,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.validateMemberRegister,
      data: {'username': username, 'email': email, 'phone': phone},
    );

    if (result.isSuccess) {
      // Use custom parsing for this specific validation endpoint
      return ApiResponseParser.parseCustomResponse<
        RegisterValidationErrorModel
      >(result.data, (json) {
        final jsonData = json as Map<String, dynamic>;
        final status = jsonData['status'] as bool? ?? false;
        final errorMessage = jsonData['error_message'] as String?;
        final errorCode = jsonData['error_code'];
        final data = jsonData['data'] as Map<String, dynamic>?;

        // Handle both success and error cases
        if (status) {
          // Success case - validation passed, return empty validation errors
          return const RegisterValidationErrorModel();
        } else {
          // Error case - extract validation errors from nested structure
          if (data != null && data.containsKey('validation_errors')) {
            final validationErrors =
                data['validation_errors'] as Map<String, dynamic>?;

            if (validationErrors != null) {
              // Transform the nested validation_errors to match our model structure
              final transformedData = <String, dynamic>{
                'username': validationErrors['username'],
                'email': validationErrors['email'],
                'phone': validationErrors['phone'],
              };

              return RegisterValidationErrorModel.fromJson(transformedData);
            }
          }

          // If no validation errors found, throw exception
          throw ValidationException(
            code: errorCode?.toString(),
            message: errorMessage ?? 'Validation failed',
            data: result.data,
          );
        }
      });
    } else {
      // Convert API client error to appropriate exception
      final errorMessage = result.errorMessage ?? 'Validation failed';
      final errorCode = result.errorCode;

      throw ValidationException(
        code: errorCode,
        message: errorMessage,
        data: result.data,
      );
    }
  }

  @override
  Future<MemberRegisterResponseModel> memberRegister({
    required MemberRegisterRequestModel request,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.memberRegister,
      data: request.toJson(),
    );

    if (result.isSuccess) {
      // Parser now returns data directly and throws ParseException on errors
      return ApiResponseParser.parseFlexibleResponse<
        MemberRegisterResponseModel
      >(result.data, MemberRegisterResponseModel.fromApiResponse);
    } else {
      // Convert API client error to appropriate exception
      final errorMessage = result.errorMessage ?? 'Registration failed';
      final errorCode = result.errorCode;

      if (errorCode == '422' || errorCode == '400') {
        throw ValidationException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      } else {
        throw ServerException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      }
    }
  }

  @override
  Future<ForgotPasswordResponseModel> forgotPasswordRequest({
    required String email,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.passwordResetRequest,
      data: {'email': email},
    );

    if (result.isSuccess) {
      // Parser now returns data directly and throws ParseException on errors
      return ApiResponseParser.parseFlexibleResponse<
        ForgotPasswordResponseModel
      >(result.data, ForgotPasswordResponseModel.fromJson);
    } else {
      // Convert API client error to appropriate exception
      final errorMessage =
          result.errorMessage ?? 'Password reset request failed';
      final errorCode = result.errorCode;

      if (errorCode == '422' || errorCode == '400') {
        throw ValidationException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      } else {
        throw ServerException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      }
    }
  }

  @override
  Future<ForgotPasswordVerifyOtpResponseModel> forgotPasswordVerifyOtp({
    required String email,
    required String otp,
    required String refCode,
    required String otpToken,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.passwordResetVerifyOtp,
      data: {
        'email': email,
        'otp': otp,
        'ref_code': refCode,
        'otp_token': otpToken,
      },
    );

    if (result.isSuccess) {
      // Parser now returns data directly and throws ParseException on errors
      return ApiResponseParser.parseFlexibleResponse<
        ForgotPasswordVerifyOtpResponseModel
      >(result.data, ForgotPasswordVerifyOtpResponseModel.fromJson);
    } else {
      // Convert API client error to appropriate exception
      final errorMessage = result.errorMessage ?? 'OTP verification failed';
      final errorCode = result.errorCode;

      if (errorCode == '422' || errorCode == '400') {
        throw ValidationException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      } else {
        throw ServerException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      }
    }
  }

  @override
  Future<CreatePasswordResponseModel> createPassword({
    required CreatePasswordRequestModel request,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.passwordResetUpdatePassword,
      data: request.toJson(),
    );

    if (result.isSuccess) {
      // Parser now returns data directly and throws ParseException on errors
      return ApiResponseParser.parseFlexibleResponse<
        CreatePasswordResponseModel
      >(result.data, CreatePasswordResponseModel.fromJson);
    } else {
      // Convert API client error to appropriate exception
      final errorMessage = result.errorMessage ?? 'Password creation failed';
      final errorCode = result.errorCode;

      if (errorCode == '422' || errorCode == '400') {
        throw ValidationException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      } else {
        throw ServerException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      }
    }
  }

  @override
  Future<Result<void>> logout({required String token}) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.logout,
      data: {'token': token},
    );

    return result.map<void>((responseData) {
      // For logout, we don't need to parse any response data
      // Just return void if the request was successful
      return;
    });
  }
}
