import 'package:mcdc/core/api/api_client.dart';
import 'package:mcdc/core/api/api_endpoints.dart';
import 'package:mcdc/core/api/model/result.dart';
import 'package:mcdc/core/api/model/api_response_parser.dart';
import 'package:mcdc/core/api/api_result_handler.dart';
import 'package:mcdc/core/error/exceptions.dart';
import 'package:mcdc/features/user/data/models/login_data_model.dart';
import 'package:mcdc/features/user/data/models/check_id_card_result_model.dart';
import 'package:mcdc/features/user/data/models/member_register_request_model.dart';
import 'package:mcdc/features/user/data/models/member_register_response_model.dart';
import 'package:mcdc/features/user/data/models/register_validation_error_model.dart';
import 'package:mcdc/features/user/data/models/forgot_password_response_model.dart';
import 'package:mcdc/features/user/data/models/forgot_password_verify_otp_response_model.dart';
import 'package:mcdc/features/user/data/models/create_password_request_model.dart';
import 'package:mcdc/features/user/data/models/create_password_response_model.dart';

abstract class UserApiDataSource {
  Future<LoginDataModel> memberLogin({
    required String username,
    required String password,
  });

  Future<LoginDataModel> consultantLogin({
    required String username,
    required String password,
  });

  Future<CheckIdCardResultModel> memberCheckIdCard({required String idCard});

  Future<RegisterValidationErrorModel> validateMemberRegister({
    required String username,
    required String email,
    required String phone,
  });

  Future<MemberRegisterResponseModel> memberRegister({
    required MemberRegisterRequestModel request,
  });

  Future<ForgotPasswordResponseModel> forgotPasswordRequest({
    required String email,
  });

  Future<ForgotPasswordVerifyOtpResponseModel> forgotPasswordVerifyOtp({
    required String email,
    required String otp,
    required String refCode,
    required String otpToken,
  });

  Future<CreatePasswordResponseModel> createPassword({
    required CreatePasswordRequestModel request,
  });

  Future<Result<void>> logout({required String token});
}

class UserApiDataSourceImpl implements UserApiDataSource {
  final ApiClient _apiClient;

  const UserApiDataSourceImpl(this._apiClient);

  @override
  Future<LoginDataModel> memberLogin({
    required String username,
    required String password,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.memberLogin,
      data: {'username': username, 'password': password},
    );
    if (result.isSuccess) {
      final parseResult =
          ApiResponseParser.parseFlexibleResponse<LoginDataModel>(
            result.data,
            LoginDataModel.fromJson,
          );
      return ApiResultHandler.handleResult(parseResult);
    } else {
      throw ApiResultHandler.resultToException(result);
    }
  }

  @override
  Future<LoginDataModel> consultantLogin({
    required String username,
    required String password,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.consultantLogin,
      data: {'username': username, 'password': password},
    );

    if (result.isSuccess) {
      final parseResult =
          ApiResponseParser.parseFlexibleResponse<LoginDataModel>(
            result.data,
            LoginDataModel.fromJson,
          );
      return ApiResultHandler.handleResult(parseResult);
    } else {
      throw ApiResultHandler.resultToException(result);
    }
  }

  @override
  Future<CheckIdCardResultModel> memberCheckIdCard({
    required String idCard,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.memberCheckIdCard,
      data: {'pid': idCard},
    );

    if (result.isSuccess) {
      final parseResult =
          ApiResponseParser.parseFlexibleResponse<CheckIdCardResultModel>(
            result.data,
            CheckIdCardResultModel.fromJson,
          );
      return ApiResultHandler.handleResult(parseResult);
    } else {
      throw ApiResultHandler.resultToException(result);
    }
  }

  @override
  Future<RegisterValidationErrorModel> validateMemberRegister({
    required String username,
    required String email,
    required String phone,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.validateMemberRegister,
      data: {'username': username, 'email': email, 'phone': phone},
    );

    final parseResult = ApiResponseParser.parseCustomResponse<
      RegisterValidationErrorModel
    >(result.data, (json) {
      final jsonData = json as Map<String, dynamic>;
      final status = jsonData['status'] as bool? ?? false;
      final errorMessage = jsonData['error_message'] as String?;
      final errorCode = jsonData['error_code'];
      final data = jsonData['data'] as Map<String, dynamic>?;

      // Handle both success and error cases
      if (status) {
        // Success case - validation passed, return empty validation errors
        return Result.success(const RegisterValidationErrorModel());
      } else {
        // Error case - extract validation errors from nested structure
        if (data != null && data.containsKey('validation_errors')) {
          final validationErrors =
              data['validation_errors'] as Map<String, dynamic>?;

          if (validationErrors != null) {
            // Transform the nested validation_errors to match our model structure
            final transformedData = <String, dynamic>{
              'username': validationErrors['username'],
              'email': validationErrors['email'],
              'phone': validationErrors['phone'],
            };

            final model = RegisterValidationErrorModel.fromJson(
              transformedData,
            );
            return Result.error(
              message: errorMessage ?? 'Validation failed',
              code: errorCode?.toString(),
              responseData: model,
            );
          }
        }

        // If no validation errors found, return error result
        return Result.error(
          message: errorMessage ?? 'Validation failed',
          code: errorCode?.toString(),
        );
      }
    });

    if (parseResult.isSuccess) {
      return parseResult.data!;
    } else if (parseResult.isError && parseResult.data != null) {
      return parseResult.data!;
    } else {
      throw ServerException(
        code: parseResult.errorCode,
        message: parseResult.errorMessage,
      );
    }
  }

  @override
  Future<MemberRegisterResponseModel> memberRegister({
    required MemberRegisterRequestModel request,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.memberRegister,
      data: request.toJson(),
    );

    // Use the new Result-based approach with custom parsing for member registration
    if (result.isSuccess) {
      final parseResult =
          ApiResponseParser.parseFlexibleResponse<MemberRegisterResponseModel>(
            result.data,
            MemberRegisterResponseModel.fromApiResponse,
          );
      return ApiResultHandler.handleResult(parseResult);
    } else {
      throw ApiResultHandler.resultToException(result);
    }
  }

  @override
  Future<ForgotPasswordResponseModel> forgotPasswordRequest({
    required String email,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.passwordResetRequest,
      data: {'email': email},
    );

    // Handle the result directly - if successful, parse the response data
    if (result.isSuccess) {
      final parseResult =
          ApiResponseParser.parseFlexibleResponse<ForgotPasswordResponseModel>(
            result.data,
            ForgotPasswordResponseModel.fromJson,
          );
      return ApiResultHandler.handleResult(parseResult);
    } else {
      // If the API call failed, convert the error result to an exception
      throw ApiResultHandler.resultToException(result);
    }
  }

  @override
  Future<ForgotPasswordVerifyOtpResponseModel> forgotPasswordVerifyOtp({
    required String email,
    required String otp,
    required String refCode,
    required String otpToken,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.passwordResetVerifyOtp,
      data: {
        'email': email,
        'otp': otp,
        'ref_code': refCode,
        'otp_token': otpToken,
      },
    );

    // Handle the result directly - if successful, parse the response data
    if (result.isSuccess) {
      final parseResult = ApiResponseParser.parseFlexibleResponse<
        ForgotPasswordVerifyOtpResponseModel
      >(result.data, ForgotPasswordVerifyOtpResponseModel.fromJson);
      return ApiResultHandler.handleResult(parseResult);
    } else {
      // If the API call failed, convert the error result to an exception
      throw ApiResultHandler.resultToException(result);
    }
  }

  @override
  Future<CreatePasswordResponseModel> createPassword({
    required CreatePasswordRequestModel request,
  }) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.passwordResetUpdatePassword,
      data: request.toJson(),
    );

    // Handle the result directly - if successful, parse the response data
    if (result.isSuccess) {
      final parseResult =
          ApiResponseParser.parseFlexibleResponse<CreatePasswordResponseModel>(
            result.data,
            CreatePasswordResponseModel.fromJson,
          );
      return ApiResultHandler.handleResult(parseResult);
    } else {
      // If the API call failed, convert the error result to an exception
      throw ApiResultHandler.resultToException(result);
    }
  }

  @override
  Future<Result<void>> logout({required String token}) async {
    final result = await _apiClient.post<dynamic>(
      ApiEndpoints.logout,
      data: {'token': token},
    );

    return result.map<void>((responseData) {
      // For logout, we don't need to parse any response data
      // Just return void if the request was successful
      return;
    });
  }
}
