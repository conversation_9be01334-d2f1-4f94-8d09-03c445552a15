import '../error/exceptions.dart';
import '../../shared/data/models/validation_errors_model.dart';
import 'model/result.dart';

/// Utility class for handling API errors and converting them to appropriate exceptions
class ApiResultHandler {
  /// Convert a Result error to an appropriate exception
  /// This method analyzes the error code and message to determine the best exception type
  static Exception resultToException(Result result) {
    if (result.isSuccess) {
      throw ArgumentError('Cannot convert successful result to exception');
    }

    final errorMessage = result.errorMessage ?? 'Unknown error occurred';
    final errorCode = result.errorCode;
    final httpStatusCode = result.errorCode;
    // Determine exception type based on error code
    if (httpStatusCode != null) {
      // HTTP status code based categorization
      if (httpStatusCode.startsWith('5')) {
        // 5xx - Server errors
        return ServerException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      } else if (httpStatusCode == '422') {
        // 422 - Validation errors
        Map<String, List<String>>? validationErrors;
        String? errorCode = result.errorCode;
        String? errorMessage = result.errorMessage;
        if (result.data != null) {
          // Try to extract validation errors if available
          try {
            final responseData = result.data as dynamic;
            errorCode = responseData['error_code'];
            errorMessage = responseData['error_message'];
            if (responseData is Map<String, dynamic> &&
                responseData['data'] != null &&
                responseData['data'].containsKey('validation_errors')) {
              final validationErrorsModel = ValidationErrorsModel.fromJson(
                responseData['data'],
              );
              validationErrors = validationErrorsModel.validationErrors;
            }
          } catch (e) {
            // Ignore parsing errors for validation data
          }
        }
        return ValidationException(
          code: errorCode,
          message: errorMessage,
          validationErrors: validationErrors,
        );
      } else if (httpStatusCode == '401' || httpStatusCode == '403') {
        // 401/403 - Authentication/Authorization errors
        return AuthException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      } else if (httpStatusCode.startsWith('4')) {
        // Other 4xx - Client errors

        if (httpStatusCode == '400') {
          Map<String, List<String>>? validationErrors;
          String? errorCode = result.errorCode;
          String? errorMessage = result.errorMessage;
          if (result.data != null) {
            // Try to extract validation errors if available
            try {
              final responseData = result.data as dynamic;
              errorCode = responseData['error_code'];
              errorMessage = responseData['error_message'];
              if (responseData is Map<String, dynamic> &&
                  responseData['data'] != null &&
                  responseData['data'].containsKey('validation_errors')) {
                final validationErrorsModel = ValidationErrorsModel.fromJson(
                  responseData['data'],
                );
                validationErrors = validationErrorsModel.validationErrors;
              }
            } catch (e) {
              // Ignore parsing errors for validation data
            }
          }
          return ValidationException(
            code: errorCode,
            message: errorMessage,
            validationErrors: validationErrors,
          );
        }

        return ClientException(
          code: errorCode,
          message: errorMessage,
          data: result.data,
        );
      }
    }

    // Message-based categorization for cases without clear error codes
    final lowerMessage = errorMessage.toLowerCase();

    if (lowerMessage.contains('network') ||
        lowerMessage.contains('connection') ||
        lowerMessage.contains('timeout')) {
      return NetworkException(
        code: errorCode,
        message: errorMessage,
        data: result.data,
      );
    } else if (lowerMessage.contains('parse') ||
        lowerMessage.contains('format') ||
        lowerMessage.contains('json')) {
      return ParseException(
        code: errorCode,
        message: errorMessage,
        data: result.data,
        originalData: result.data,
      );
    } else if (lowerMessage.contains('validation')) {
      return ValidationException(
        code: errorCode,
        message: errorMessage,
        data: result.data,
      );
    } else if (lowerMessage.contains('unauthorized') ||
        lowerMessage.contains('forbidden') ||
        lowerMessage.contains('authentication')) {
      return AuthException(
        code: errorCode,
        message: errorMessage,
        data: result.data,
      );
    }

    // Default to ServerException for unknown errors
    return ServerException(
      code: errorCode,
      message: errorMessage,
      data: result.data,
    );
  }

  /// Helper method to throw appropriate exception from Result
  static void throwFromResult(Result result) {
    throw resultToException(result);
  }

  /// Helper method to handle Result in data source methods
  /// Returns the data if successful, throws appropriate exception if error
  static T handleResult<T>(Result<T> result) {
    if (result.isSuccess) {
      return result.data!;
    } else {
      throw resultToException(result);
    }
  }
}
